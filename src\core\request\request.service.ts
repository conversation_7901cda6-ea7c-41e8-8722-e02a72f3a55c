import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { EntityStatus } from '@common/entities/base.entity';
import { DatabaseAction } from '@common/enums/dbaction.enum';
import { RequestStatus } from '@common/enums/request-status.enum';
import { MailService } from '@core/notification/mail-service/mail.service';
import { RequestValidatorService } from '@core/request/request.validator.service';
import { User } from '@core/security/user/entities/user.entity';
import { UserService } from '@core/security/user/user.service';
import { StoreService } from '@core/store/store.service';
import {
  BadRequestException,
  ConflictException,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import * as _ from 'lodash';
import {
  IPaginationOptions,
  paginate,
  Pagination,
} from 'nestjs-typeorm-paginate';
import { EntityManager, ILike, Repository } from 'typeorm';
import { AssignRequestDto } from './dto/assign-request.dto';
import { RequestTableDto } from './dto/request-table.dto';
import { RequestDto } from './dto/request.dto';
import { Request } from './entities/request.entity';
import { LoggerService } from '@common/logger/logger.service';
import { CoreUtils } from '@common/utils/core.utils';
import { ItemService } from '@core/store/item/item.service';
import { ItemAuditService } from '@core/store/item-audit/item-audit.service';
import { RequestAudit } from '@core/request/request-audit/entities/request-audit.entity';
import { ItemAudit } from '@core/store/item-audit/entities/item-audit.entity';
import { ItemAuditDto } from '@core/store/item-audit/dto/item-audit.dto';
import { ItemUnit } from '@core/store/item-unit/entities/item-unit.entity';
import { DepartmentService } from '@core/security/department/department.service';
import { CollectRequestItemsDto } from './dto/collect-request-items.dto';
import { ReturnRequestDto } from './dto/return-request.dto';
import { IEntityServiceStrategy } from '@common/utils/i.entity.service.strategy';
import { PaginationQueryParams } from '@common/utils/pagination_query_params.type';
import { StorageInterface } from '@core/storage/storage.interface';
import { CoreConstants } from '@common/utils/core.constants';
import { ChurchLetter } from '@core/request/dto/type/church-letter.type';

@Injectable()
export class RequestService implements IEntityServiceStrategy<Request> {
  frontOfficeUrl: string;
  approvalPath: string;
  returnFormPath: string;

  constructor(
    @InjectRepository(Request)
    private readonly requestRepository: Repository<Request>,
    private readonly requestValidator: RequestValidatorService,
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly mailService: MailService,
    private readonly storeService: StoreService,
    private readonly itemService: ItemService,
    private readonly itemAuditService: ItemAuditService,
    private readonly departmentService: DepartmentService,
    private readonly configService: ConfigService,
    private readonly userService: UserService,
    private readonly logger: LoggerService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    @Inject('StorageService') private readonly storageService: StorageInterface,
  ) {
    this.logger.setContext(RequestService.name);

    this.frontOfficeUrl = this.configService.get<string>('frontOfficeUrl');
    this.approvalPath = this.configService.get<string>('approvalPath');
    this.returnFormPath = this.configService.get<string>('returnFormPath');
  }

  async create(newRequest: Request) {
    return await this.entityManager.transaction(async (manager) => {
      const itemAudits = [];

      const requesterDepartment = await this.departmentService.findByPk(
        newRequest.requesterDepartmentId,
      );

      for (const itemAudit of newRequest.audit.items) {
        const foundItem = await this.itemService.findByPk(
          Number(itemAudit.itemId),
        );

        if (!foundItem) {
          throw new NotFoundException(
            `Item with ID ${itemAudit.itemId} not found`,
          );
        }

        if (foundItem.availableQuantity === 0) {
          throw new ConflictException(
            `${foundItem.name} is currently not available`,
          );
        }
        if (itemAudit.quantityLeased > foundItem.availableQuantity) {
          throw new ConflictException(
            `Requested quantity of ${foundItem.name} exceeds available quantity. Available Quantity: ${foundItem.availableQuantity}, Requested Quantity: ${itemAudit.quantityLeased}`,
          );
        }
        itemAudit.itemId = foundItem.id;
        itemAudit.itemName = foundItem.name;
        itemAudits.push({ itemAudit, foundItem });
      }

      newRequest.createdBy = newRequest.requesterName;
      newRequest.requesterHodName = requesterDepartment.hodName;
      newRequest.requesterHodEmail = requesterDepartment.hodEmail;
      newRequest.requesterHodPhone = requesterDepartment.hodPhone;
      newRequest.requestStatus = RequestStatus.PENDING;
      const audit = new RequestAudit();
      audit.assigned = false;
      audit.collected = false;
      audit.completed = false;
      audit.items = [];
      newRequest.audit = audit;

      for (const { itemAudit, foundItem } of itemAudits) {
        // actualItem.availableQuantity -= itemAudit.quantityLeased;
        await manager.save(foundItem); // replaces this.itemService.update()
        newRequest.audit.items.push(itemAudit);
      }

      newRequest.durationOfUse = CoreUtils.calculateRemainingDuration(
        newRequest.dateOfReturn,
      );

      await manager.save(newRequest).then(async (savedRequest) => {
        const link = `${this.frontOfficeUrl}/${this.approvalPath}/${savedRequest.id}`;
        await this.mailService.sendSmtpMail(
          requesterDepartment.hodEmail,
          'Request Verification',
          `Dear ${requesterDepartment.hodName},
        \n This request has been raised by ${newRequest.requesterName} from ${
            requesterDepartment.name
          } department, ${
            newRequest.isChurch
              ? newRequest.churchName
              : newRequest.ministryName
          }.
        \n Kindly review and approve or decline via this link: ${link}.
        \n God bless you as you do so.`,
        );
      });
    });
  }

  async getRequest(id: number): Promise<any> {
    const existingRequest = await this.findByPk(id);
    if (!existingRequest) {
      throw new NotFoundException('Request not found');
    }

    let assigneeName = null;
    const assigneeId = existingRequest?.audit?.assignee;
    if (assigneeId) {
      const assignee = await this.userService.findByPk(Number(assigneeId));
      if (assignee) {
        assigneeName = `${assignee.firstName} ${assignee.lastName}`;
      }
    }

    return {
      ...existingRequest,
      summary: {
        ...existingRequest,
        audit: {
          ...existingRequest?.audit,
          assigneeName,
        },
      },
    };
  }

  async update(request: Request): Promise<Request | undefined> {
    await this.requestValidator.validate(request, DatabaseAction.UPDATE);
    return await this.requestRepository.save(request);
  }

  async findAll(): Promise<Array<Request>> {
    return await this.requestRepository.find();
  }

  async findByPk(id: number): Promise<Request> {
    return await this.requestRepository.findOneBy({ id });
  }

  async findFirstBy(where: any): Promise<Request> {
    return await this.requestRepository.findOneBy(where);
  }

  async delete(id: number): Promise<void> {
    await this.requestRepository.delete(id);
  }

  async activate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const request: Request = await this.findByPk(id);
      request.status = EntityStatus.ACTIVE;
      request.status = EntityStatus.ACTIVE;
      await this.update(request);
    });
  }

  async deactivate(ids: Array<number>): Promise<void> {
    ids.map(async (id) => {
      const request: Request = await this.findByPk(id);
      request.status = EntityStatus.INACTIVE;
      await this.update(request);
    });
  }

  async getPaginatedRequests(
    params: PaginationQueryParams,
    route: string,
  ): Promise<Pagination<RequestTableDto>> {
    const { page, limit, search, filter, id } = params;

    const orConditions: any[] = [];

    // Search OR conditions
    if (search) {
      orConditions.push(
        { churchName: ILike(`%${search}%`) },
        { ministryName: ILike(`%${search}%`) },
      );
    }

    // Filter OR conditions
    if (filter) {
      orConditions.push(
        { isChurch: filter },
        { isMinistry: filter },
        { summary: { requestStatus: filter } },
      );
    }

    // Base condition: requesterDepartmentId is always AND-ed
    const baseCondition = id ? { requesterDepartmentId: id } : {};

    const options = {
      page,
      limit: limit > 100 ? 100 : limit,
      route,
    };

    const where =
      orConditions.length > 0
        ? orConditions.map((condition) => ({ ...baseCondition, ...condition }))
        : baseCondition;

    const pagination = await paginate<Request>(
      this.requestRepository,
      options,
      {
        where,
        order: { createdAt: 'DESC' },
        relations: ['summary'],
      },
    );

    const departmentIds = pagination.items.map(
      (item) => item.requesterDepartmentId,
    );

    const departments = await this.departmentService.fetchDepartmentsByPks(
      departmentIds,
    );

    const departmentMap = new Map(departments.map((dep) => [dep.id, dep]));

    const dtoList = await Promise.all(
      pagination.items.map(async (request) => {
        const requestDto = await this.classMapper.mapAsync(
          request,
          Request,
          RequestTableDto,
        );
        requestDto.requesterDepartment =
          departmentMap.get(Number(request.requesterDepartmentId))?.name || '';
        return requestDto;
      }),
    );

    return new Pagination(dtoList, pagination.meta, pagination.links);
  }

  async search(query: string): Promise<Array<Request>> {
    return await this.requestRepository.find({
      where: [
        { churchName: ILike(`%${query}%`) },
        { ministryName: ILike(`%${query}%`) },
        { requesterName: ILike(`%${query}%`) },
      ],
    });
  }

  async paginatedSearch(
    query: string,
    page = 1,
    limit = 10,
  ): Promise<Array<Request>> {
    const [results] = await this.requestRepository.findAndCount({
      where: [
        { churchName: ILike(`%${query}%`) },
        { ministryName: ILike(`%${query}%`) },
      ],
      skip: (page - 1) * limit,
      take: limit,
    });
    return results;
  }

  async approveRequest(requestId: number) {
    const request = await this.findByPk(requestId);
    if (!request) {
      throw new NotFoundException('Request not found');
    }

    if (
      request.requestStatus === RequestStatus.APPROVED ||
      request.requestStatus === RequestStatus.COMPLETED ||
      request.requestStatus === RequestStatus.DECLINED
    ) {
      throw new ConflictException(
        `Request has already been ${request.requestStatus}`,
      );
    }

    const department = await this.departmentService.findByPk(
      request.requesterDepartmentId,
    );
    if (!department) {
      throw new NotFoundException('Department not found');
    }

    for (const item of request.audit.items) {
      const actualItem = await this.itemService.findByPk(item.itemId);
      if (!actualItem) {
        throw new NotFoundException(`Item with ID ${item.itemId} not found`);
      }
      item.quantityLeased = Number(item.quantityLeased);
      actualItem.availableQuantity = Number(actualItem.availableQuantity);
      if (item.quantityLeased > actualItem.availableQuantity) {
        throw new ConflictException(
          `Requested quantity of ${actualItem.name} exceeds available quantity. Available Quantity: ${actualItem.availableQuantity}, Requested Quantity: ${item.quantityLeased}`,
        );
      }

      actualItem.availableQuantity -= item.quantityLeased;
      await this.itemService.update(actualItem); // Update the item in the database
    }

    request.requestStatus = RequestStatus.APPROVED;
    await this.requestRepository.save(request);

    //send mail to requester
    const requesterSubject = 'Approved Request Notification';
    const requesterMessage = `Dear ${request.requesterName},\n Your request has been Approved by your HOD.\n God bless you!`;

    //TODO: IN-APP notification or WEB notification or EMAIL.
    // A notification needs to be sent to the super admin, stating that a request has been approved.

    const superAdminUsers: Array<User> =
      await this.userService.getAllSuperAdmins();

    for (const superAdminUser of superAdminUsers) {
      const link = `${this.frontOfficeUrl}/${this.approvalPath}/${request.id}`;

      await this.mailService.sendSmtpMail(
        superAdminUser.email,
        'Approved Request Notification',
        `Dear ${superAdminUser.firstName},\n The request with ID ${requestId} has been approved by the HOD of ${department.name} - ${request.requesterHodName} .\n 
        \n Kindly assign a logistics member to this request via this link: ${link}.
        \n God bless you as you do so!`,
      );
    }

    await this.mailService.sendSmtpMail(
      request?.requesterEmail,
      requesterSubject,
      requesterMessage,
    );
  }

  async declineRequest(requestId: number) {
    const request = await this.findByPk(requestId);
    if (!request) {
      throw new NotFoundException('Request not found');
    }

    if (
      request.requestStatus === RequestStatus.DECLINED ||
      request.requestStatus === RequestStatus.APPROVED ||
      request.requestStatus === RequestStatus.COMPLETED
    ) {
      throw new ConflictException(
        `Request has already been ${request.requestStatus}`,
      );
    }
    request.requestStatus = RequestStatus.DECLINED;

    await this.requestRepository.save(request);

    //send mail to requester
    const requesterSubject = 'Decline Notification';
    const requesterMessage = `Dear ${request.requesterName},\n Your request has been declined. Kindly reach out to your HOD.\n God bless you!`;

    await this.mailService.sendSmtpMail(
      request?.requesterEmail,
      requesterSubject,
      requesterMessage,
    );
  }

  async assignRequest(
    requestId: number,
    assignRequestDto: AssignRequestDto,
    user: User,
  ) {
    const request = await this.findByPk(requestId);
    if (!request) {
      throw new NotFoundException('Request not found');
    }
    if (
      request.requestStatus === RequestStatus.PENDING ||
      request.requestStatus === RequestStatus.DECLINED
    ) {
      throw new ConflictException(
        'Request must be approved before it can be assigned',
      );
    }
    if (request.requestStatus === RequestStatus.ASSIGNED) {
      throw new ConflictException('Request has already been assigned');
    }

    const assignee = await this.userService.findByPk(assignRequestDto.userId);
    if (!assignee) {
      throw new NotFoundException('User not found');
    }

    if (!request.audit) {
      throw new ConflictException('Request audit is not initialized');
    }

    request.audit.assigner = `${user.firstName} ${user.lastName}`;
    request.audit.assignee = assignee.id;
    request.audit.assigned = true;
    request.audit.dateAssigned = new Date();

    const itemsList = request.audit.items
      .map(
        (item, index) =>
          `${index + 1}. ${item.itemName} - Quantity (${item.quantityLeased})`,
      )
      .join('\n  ');
    //update requestStatus
    request.requestStatus = RequestStatus.ASSIGNED;
    await this.requestRepository.save(request);
    const subject = 'Assignment Notification';

    const link = `${this.frontOfficeUrl}/${this.approvalPath}/${request.id}`;

    const message = `Hello ${assignee.firstName},\n You have been assigned to a new task to supervise a request.\n
  Here are the list of items to be supervised:\n
  ${itemsList}\n
   Please review the task details at your earliest convenience and begin work as soon as possible.\n 
   Kindly update this request once the items has been taken out from the store via this link: ${link}. \n
   God bless you!`;

    const requesterSubject = 'Approval Notification';
    const requesterMessage = `Dear ${request.requesterName},\n Your request has been approved for pick up. Please reach out to ${assignee.firstName} in the 
    logistics department to pick up the items. \n Phone: ${assignee.phoneNumber}.\n God bless you!`;
    // ${assignee.department.name}

    await Promise.all([
      this.mailService.sendSmtpMail(assignee?.email, subject, message),
      this.mailService.sendSmtpMail(
        request?.requesterEmail,
        requesterSubject,
        requesterMessage,
      ),
    ]);
  }

  async markRequestAsCollected(
    requestId: number,
    collectRequestItemsDto: CollectRequestItemsDto,
  ) {
    return await this.entityManager.transaction(async (manager) => {
      const request = await manager.findOne(Request, {
        where: { id: requestId },
        relations: ['audit', 'audit.items'],
      });

      if (!request) {
        throw new NotFoundException('Request not found');
      }

      if (request.requestStatus !== RequestStatus.ASSIGNED) {
        throw new ConflictException(
          'Request must be assigned before items can be collected',
        );
      }

      const assignedUser = await this.userService.findByPk(
        request.audit.assignee,
      );

      if (!assignedUser) {
        throw new NotFoundException('Assigned user not found');
      }

      for (const itemDto of collectRequestItemsDto.items) {
        const actualItem = await this.itemService.findByPk(itemDto.itemId);

        if (!actualItem) {
          throw new NotFoundException(
            `Item with ID ${itemDto.itemId} not found`,
          );
        }

        if (typeof itemDto.leasedDate === 'string') {
          const parsedDate = new Date(itemDto.leasedDate);
          if (isNaN(parsedDate.getTime())) {
            throw new BadRequestException(
              `Invalid date format for leasedDate: ${itemDto.leasedDate}`,
            );
          }
          itemDto.leasedDate = parsedDate;
        }

        if (itemDto.quantityReleased < itemDto.quantityLeased) {
          const difference = itemDto.quantityLeased - itemDto.quantityReleased;

          actualItem.availableQuantity =
            Number(actualItem.availableQuantity) + Number(difference);

          await this.mailService.sendSmtpMail(
            `${request.requesterHodEmail}`,
            'Item quantity release issue',
            `Hello ${request.requesterHodName}, the quantity released for the item, ${actualItem.name} is less than the quantity leased.
           \n The quantity released is ${itemDto.quantityReleased} and quantity leased was ${itemDto.quantityLeased}.\n Kindly review.`,
          );

          const superAdmins = await this.userService.getAllSuperAdmins();

          for (const superAdmin of superAdmins) {
            await this.mailService.sendSmtpMail(
              superAdmin.email,
              'Item quantity release issue',
              `Hello Pastor, the quantity released for the item, ${actualItem.name} is less than the quantity leased.\n The quantity released is ${itemDto.quantityReleased} and quantity leased was ${itemDto.quantityLeased}.
             \n Kindly review.`,
            );
          }

          await this.mailService.sendSmtpMail(
            `${assignedUser.email}`,
            'Item quantity release issue',
            `Hello ${assignedUser.firstName}, the quantity released for the item, ${actualItem.name} is less than the quantity leased.
           \n The quantity released is ${itemDto.quantityReleased} and quantity leased was ${itemDto.quantityLeased}.\n
           Kindly review and proceed with the request collection process.`,
          );
        }

        // Update item unit entities for each unit entry
        for (const unitDto of itemDto.units) {
          const itemUnit = await manager.findOne(ItemUnit, {
            where: { serialNumber: unitDto.serialNumber },
          });

          if (itemUnit) {
            itemUnit.store = { id: unitDto.storeId };
            itemUnit.condition = unitDto.condition;
            await manager.save(ItemUnit, itemUnit);
          }
        }

        const requestItem = request.audit.items.find(
          (item) => item.itemId === itemDto.itemId,
        );
        if (requestItem) {
          requestItem.quantityReleased = Number(itemDto.quantityReleased);
          requestItem.releasedDate = new Date(itemDto.leasedDate);
          requestItem.units = itemDto.units;
        }

        // Update the item in the database
        await manager.save(actualItem);
      }

      // Update the request status and audit details
      request.audit.collected = true;
      request.audit.collectedBy = `${assignedUser.firstName} ${assignedUser.lastName}`;
      request.audit.collectedDate = new Date();
      request.requestStatus = RequestStatus.COLLECTED;

      await manager.save(request);

      // Notify stakeholders about the completion
      const superAdmins = await this.userService.getAllSuperAdmins();

      for (const superAdmin of superAdmins) {
        await this.mailService.sendSmtpMail(
          superAdmin.email,
          'Request Collected',
          `Hello Pastor, the request, ${request.descriptionOfRequest} has been collected by ${assignedUser.firstName} ${assignedUser.lastName}. Kindly review.`,
        );
      }
    });
  }

  async markRequestAsCompleted(
    requestId: number,
    returnRequestItemsDto: ReturnRequestDto,
  ) {
    const request = await this.findByPk(requestId);

    if (!request) {
      throw new NotFoundException('Request not found');
    }

    if (request.requestStatus !== RequestStatus.COLLECTED) {
      throw new ConflictException(
        'Request must be collected before items can be returned',
      );
    }

    const assignedUser = await this.userService.findByPk(
      request.audit.assignee,
    );

    if (!assignedUser) {
      throw new NotFoundException('Assigned user not found');
    }

    for (const itemDto of returnRequestItemsDto.items) {
      const actualItem = await this.itemService.findByPk(itemDto.itemId);

      if (!actualItem) {
        throw new NotFoundException(`Item with ID ${itemDto.itemId} not found`);
      }

      if (typeof itemDto.returnedDate === 'string') {
        const parsedDate = new Date(itemDto.returnedDate);
        if (isNaN(parsedDate.getTime())) {
          throw new BadRequestException(
            `Invalid date format for returnedDate: ${itemDto.returnedDate}`,
          );
        }
        itemDto.returnedDate = parsedDate;
      }

      if (itemDto.quantityReturned < itemDto.quantityReleased) {
        // const difference = itemDto.quantityReleased - itemDto.quantityReturned;

        // actualItem.availableQuantity =
        //   Number(actualItem.availableQuantity) + Number(difference);

        await this.mailService.sendSmtpMail(
          `${request.requesterHodEmail}`,
          'Item quantity returned issue',
          `Hello ${request.requesterHodName}, the quantity returned for the item, ${actualItem.name} is less than the quantity released.
         \n The quantity returned is ${itemDto.quantityReturned} and quantity released was ${itemDto.quantityReleased}.\n Kindly review.`,
        );

        const superAdmins = await this.userService.getAllSuperAdmins();

        for (const superAdmin of superAdmins) {
          await this.mailService.sendSmtpMail(
            superAdmin.email,
            'Item quantity returned issue',
            `Hello Pastor, the quantity returned for the item, ${actualItem.name} is less than the quantity released.\n The quantity returned is ${itemDto.quantityReturned} and quantity released was ${itemDto.quantityReleased}.
           \n Kindly review.`,
          );
        }

        await this.mailService.sendSmtpMail(
          `${assignedUser.email}`,
          'Item quantity release issue',
          `Hello ${assignedUser.firstName}, the quantity released for the item, ${actualItem.name} is less than the quantity leased.
         \n The quantity released is ${itemDto.quantityReleased} and quantity leased was ${itemDto.quantityLeased}.\n
         Kindly review and proceed with the request collection process.`,
        );
      }

      actualItem.availableQuantity =
        Number(actualItem.availableQuantity) + Number(itemDto.quantityReturned);

      const requestItem = request.audit.items.find(
        (item) => item.itemId === itemDto.itemId,
      );
      if (requestItem) {
        requestItem.quantityReturned = Number(itemDto.quantityReturned);
        requestItem.returnedDate = new Date(itemDto.returnedDate);
      }
      // Update the item in the database
      await this.itemService.update(actualItem);
    }

    // Update the request status and audit details
    request.audit.completed = true;
    request.audit.completedBy = `${assignedUser.firstName} ${assignedUser.lastName}`;
    request.audit.completedDate = new Date();
    request.requestStatus = RequestStatus.COMPLETED;

    await this.requestRepository.save(request);

    // Notify stakeholders about the completion
    const superAdmins = await this.userService.getAllSuperAdmins();

    for (const superAdmin of superAdmins) {
      await this.mailService.sendSmtpMail(
        superAdmin.email,
        'Request Completed',
        `Hello Pastor, the request, ${request.descriptionOfRequest} has been completed by ${assignedUser.firstName} ${assignedUser.lastName}. Kindly review.`,
      );
    }
  }

  async retrieveAllRequestedItems(requestId: number) {
    const request = await this.findByPk(requestId);
    if (!request) {
      throw new NotFoundException('Request not found');
    }

    const itemAudits = await this.itemAuditService.findAllItemAuditByRequestId(
      requestId,
    );
    return await this.classMapper.mapArrayAsync(
      itemAudits,
      ItemAudit,
      ItemAuditDto,
    );
  }

  async fetchAllRequestsByDepartmentId(
    departmentId: number,
  ): Promise<Array<RequestDto>> {
    const departmentRequests = await this.requestRepository.find({
      where: { requesterDepartmentId: departmentId },
    });

    if (_.isEmpty(departmentRequests)) {
      throw new NotFoundException('No requests found for this department');
    }

    return await this.classMapper.mapArrayAsync(
      departmentRequests,
      Request,
      RequestDto,
    );
  }

  async deleteRequest(id: number): Promise<void> {
    const request = await this.findByPk(id);
    if (!request) {
      throw new NotFoundException('Request not found');
    }
    await this.requestRepository.delete(id);
  }

  async getPaginatedRequestsByAssignee(
    assigneeId: number,
    options: IPaginationOptions,
  ): Promise<Pagination<RequestTableDto>> {
    const queryBuilder = this.requestRepository
      .createQueryBuilder('request')
      .leftJoinAndSelect('request.summary', 'summary')
      .leftJoinAndSelect('summary.audit', 'audit')
      .where('audit.assignee = :assigneeId', { assigneeId });

    const pagination = await paginate<Request>(queryBuilder, options);

    // 1. Get all department IDs from the paginated requests
    const departmentIds = pagination.items.map(
      (item) => item.requesterDepartmentId,
    );

    // 2. Fetch department details
    const departments = await this.departmentService.fetchDepartmentsByPks(
      departmentIds,
    );
    const departmentMap = new Map(departments.map((dep) => [dep.id, dep]));

    // 3. Map department name to each request DTO
    const dtoList = await Promise.all(
      pagination.items.map(async (request) => {
        const requestDto = await this.classMapper.mapAsync(
          request,
          Request,
          RequestTableDto,
        );
        requestDto.requesterDepartment =
          departmentMap.get(Number(request.requesterDepartmentId))?.name || '';
        return requestDto;
      }),
    );

    return new Pagination(dtoList, pagination.meta, pagination.links);
  }

  async uploadChurchLetter(file: Buffer, folder: string, extension: string) {
    try {
      const uploadResult = await this.storageService.uploadFile(
        file,
        null, // hash parameter (not needed for church letters)
        folder,
        extension,
      );

      return {
        url: uploadResult.secure_url || uploadResult.url,
        publicId: uploadResult.public_id,
      } as ChurchLetter;
    } catch (error) {
      this.logger.error(`Failed to upload church letter: ${error.message}`);
      throw new Error(`Failed to upload church letter: ${error.message}`);
    }
  }
}
