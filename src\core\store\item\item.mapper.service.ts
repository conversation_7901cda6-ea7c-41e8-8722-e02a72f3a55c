import {
  createMap,
  forMember,
  ignore,
  mapFrom,
  Mapper,
  MappingProfile,
} from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { CreateItemDto } from './dto/create-item.dto';
import { DepartmentItemDto } from './dto/department-item.dto';
import { ItemConditionStatusUpdateDto } from './dto/item-condition-status-update.dto';
import { ItemDto } from './dto/item.dto';
import { UpdateItemDto } from './dto/update-item.dto';
import { Item } from './entities/item.entity';

@Injectable()
export class ItemMapperService extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile(): MappingProfile {
    return (mapper) => {
      createMap(
        mapper,
        Item,
        ItemDto,
        forMember(
          (destination) => destination.actualQuantity,
          mapFrom((source) => Number(source.actualQuantity)),
        ),
        forMember(
          (destination) => destination.availableQuantity,
          mapFrom((source) => Number(source.availableQuantity)),
        ),
      );

      createMap(mapper, ItemDto, Item);
      createMap(
        mapper,
        CreateItemDto,
        Item,
        forMember(
          (destination) => destination.department,
          mapFrom((source) => ({ id: source.departmentId })),
        ),
      );

      createMap(
        mapper,
        Item,
        DepartmentItemDto,
        forMember(
          (destination) => destination.availableQuantity,
          mapFrom((source) => Number(source.availableQuantity)),
        ),
      );

      // Enhanced UpdateItemDto to Item mapping
      createMap(
        mapper,
        UpdateItemDto,
        Item,
        // Ignore itemUnits in the main item mapping since you handle them separately
        forMember((destination) => destination.itemUnits, ignore()),
        // Handle potential number conversions
        forMember(
          (destination) => destination.actualQuantity,
          mapFrom((source) =>
            source.actualQuantity ? Number(source.actualQuantity) : undefined,
          ),
        ),
      );

      createMap(mapper, ItemConditionStatusUpdateDto, Item);
    };
  }
}
